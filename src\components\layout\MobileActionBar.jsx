import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Tooltip from "../tooltips/Tooltip";
import { InfoTooltip, SuccessTooltip } from "../tooltips/EnhancedTooltip";
import ShareButton from "./ShareButton";
import ThemeSelector from "./ThemeSelector";

const MobileActionBar = ({ 
  roadmapData, 
  roadmapId, 
  isDownloading, 
  onDownload, 
  onEdit 
}) => {
  const [showSecondaryActions, setShowSecondaryActions] = useState(false);
  const navigate = useNavigate();

  return (
    <>
      {/* Primary Action Bar */}
      <div className="sm:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-3 z-40">
        <div className="flex items-center space-x-3 max-w-md mx-auto">
          {/* Primary Actions */}
          <InfoTooltip
            content="Edit roadmap content, add tasks, and modify structure"
            position="top"
            maxWidth="250px"
          >
            <button
              onClick={onEdit}
              className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 min-h-[48px] font-medium shadow-sm active:scale-95"
              aria-label={`Edit ${roadmapData?.title} roadmap`}
            >
              <svg
                className="w-5 h-5 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              <span className="text-sm font-medium">Edit</span>
            </button>
          </InfoTooltip>

          <SuccessTooltip
            content="Download the current roadmap as a JSON file for backup or sharing"
            position="top"
            maxWidth="250px"
          >
            <button
              onClick={onDownload}
              disabled={isDownloading}
              className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 min-h-[48px] font-medium shadow-sm active:scale-95"
              aria-label={`Download ${roadmapData?.title} roadmap as JSON`}
            >
              {isDownloading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin flex-shrink-0"></div>
                  <span className="text-sm font-medium">Saving...</span>
                </>
              ) : (
                <>
                  <svg
                    className="w-5 h-5 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  <span className="text-sm font-medium">Download</span>
                </>
              )}
            </button>
          </SuccessTooltip>

          {/* More Actions Button */}
          <Tooltip
            content="More actions: Share and Theme settings"
            position="top"
            maxWidth="200px"
          >
            <button
              onClick={() => setShowSecondaryActions(!showSecondaryActions)}
              className="flex items-center justify-center w-12 h-12 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200 shadow-sm active:scale-95"
              aria-label="More actions"
            >
              <svg
                className={`w-5 h-5 transition-transform duration-200 ${
                  showSecondaryActions ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                />
              </svg>
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Secondary Actions Overlay */}
      {showSecondaryActions && (
        <div className="sm:hidden fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-end">
          <div className="w-full bg-white dark:bg-gray-800 rounded-t-2xl border-t border-gray-200 dark:border-gray-700 p-4 pb-8 shadow-2xl">
            <div className="flex justify-center mb-4">
              <div className="w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            </div>
            
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 text-center">
              More Actions
            </h3>
            
            <div className="space-y-3 max-w-sm mx-auto">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Share Roadmap
                </span>
                <ShareButton roadmapTitle={roadmapData?.title} />
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Theme
                </span>
                <ThemeSelector />
              </div>
            </div>
            
            <button
              onClick={() => setShowSecondaryActions(false)}
              className="w-full mt-6 py-3 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors duration-200 active:scale-95"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Mobile Bottom Padding */}
      <div className="sm:hidden h-20"></div>
    </>
  );
};

export default MobileActionBar;
